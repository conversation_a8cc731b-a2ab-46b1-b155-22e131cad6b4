CREATE TABLE `crops_farm_disease_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `disease_type_id` int(11) DEFAULT NULL,
  `disease_name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `action_reason` varchar(255) DEFAULT NULL,
  `number_of_plants` int(11) DEFAULT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `hectares` decimal(10,2) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
