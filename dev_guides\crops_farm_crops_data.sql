CREATE TABLE `crops_farm_crops_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `crop_id` int(11) NOT NULL,
  `action_type` enum('add','remove') NOT NULL,
  `action_reason` varchar(100) NOT NULL,
  `number_of_plants` int(11) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `action_date` date NOT NULL,
  `hectares` decimal(10,2) NOT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `status` enum('active','inactive','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
