CREATE TABLE `farm_harvest_processing_machinery_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `crop_id` int(11) DEFAULT NULL,
  `owner_type` enum('sme','farmer') DEFAULT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `machinery_code` varchar(100) DEFAULT NULL,
  `machinery_name` varchar(255) DEFAULT NULL,
  `machinery_type` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `machinery_conditions` varchar(100) DEFAULT NULL,
  `operation_issues` text DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `llg_id` int(11) DEFAULT NULL,
  `ward_id` int(11) DEFAULT NULL,
  `lon` varchar(50) DEFAULT NULL,
  `lat` varchar(50) DEFAULT NULL,
  `inspection_date` date DEFAULT NULL,
  `inspection_by` int(11) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
