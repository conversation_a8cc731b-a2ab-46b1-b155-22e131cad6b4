CREATE TABLE `crops_farm_tools` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tool_type` enum('general','speciality') NOT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `tool_name` varchar(255) NOT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
