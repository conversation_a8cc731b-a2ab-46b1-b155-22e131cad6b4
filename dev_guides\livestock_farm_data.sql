CREATE TABLE `livestock_farm_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exercise_id` int(11) DEFAULT NULL,
  `block_id` int(11) NOT NULL,
  `livestock_id` int(11) NOT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `he_total` int(11) DEFAULT NULL,
  `she_total` int(11) DEFAULT NULL,
  `pasture_type` varchar(100) DEFAULT NULL,
  `growth_stage` varchar(100) DEFAULT NULL,
  `comments` text DEFAULT NULL,
  `action_date` date DEFAULT NULL,
  `cost_per_livestock` decimal(10,2) DEFAULT NULL,
  `low_price_per_livestock` decimal(10,2) DEFAULT NULL,
  `high_price_per_livestock` decimal(10,2) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `status` enum('active','inactive','deleted') NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
